import { useEffect, useRef, useState } from "react";
import { TabsContent } from "@/components/ui/tabs";
import PostsMyProfile from "./posts";
import ServicesMyProfile from "./services";
import { EventsMyProfile } from "./events";
import { ProfileMyProfile } from "./profile";

import * as Tabs from "@radix-ui/react-tabs";

import { Button } from "@/components/ui/button";
import OtherServicesMProfile from "./otherServices";
import useAuth from "@/hook";
import useProfile from "@/hook/profileData";
import { themes } from "../../../../../theme";
import { GetUserOrderStats } from "@/services/ordersServices";
type ProfileMyProfileProps = {
  activeBgColor?: string; // New prop for dynamic active border color
  isOtherProfile?: any;
  otherUserID?: any;
  isOtherProfileStatus: boolean;
  setToggleMain: any;
  selectedTabs: any;
  selectedTabsStatus: boolean;
  setSelectedTabs: any;
  isRefatch: any;
  setSelectedProfileTabs: any;
  selectedProfileTabs: any;
  setIsOpen?: React.Dispatch<React.SetStateAction<boolean>>;
};
export function TabsMyProfile({
  activeBgColor,
  isOtherProfile,
  otherUserID,
  isOtherProfileStatus,
  setToggleMain,
  selectedTabs,
  selectedTabsStatus,
  setSelectedTabs,
  isRefatch,
  setSelectedProfileTabs,
  selectedProfileTabs,
  setIsOpen,
}: ProfileMyProfileProps) {
  const postsRef = useRef<HTMLDivElement>(null);

  // const auth = useAuth();
  const profile = useProfile("XA8hBI4KqXgRpw8p587qekYuKks2");

  // Extract post lengths safely
  const postsLength = profile?.profileData?.posts?.length || 0;
  const starredPostsLength = profile?.profileData?.starredPosts?.length || 0;

  const [isProfile, setIsProfile] = useState(selectedTabs);

  const [activeTabs, setActiveTabs] = useState("Posts");
  const [activePostCategory, setActivePostCategory] = useState("");
  const [orderStats, setOrderStats] = useState<number>(0);
  const [customOrderStats, setCustomOrderStats] = useState<number>(0);

  // console.log(selectedTabs);

  // console.log(isProfile);
  const GetUserOrderStatsData = async (userId: string) => {
    try {
      const resp = await GetUserOrderStats(userId);
      console.log({ resp });

      if (resp) {
        setOrderStats(resp?.message?.totalOrders);
        setCustomOrderStats(resp?.message?.customOrdersCount);
      }
    } catch (error) {
      console.log({ error });
    }
  };

  console.log("XA8hBI4KqXgRpw8p587qekYuKks2");
  GetUserOrderStatsData("XA8hBI4KqXgRpw8p587qekYuKks2");
  useEffect(() => {
    // GetUserOrderStatsData(isOtherProfile ? otherUserID : auth?.userData?.uid);

    // console.log(isOtherProfile ? otherUserID : auth?.userData?.uid);
    setIsProfile(selectedTabs ? selectedTabs : "Posts");
    // alert("Click");
  }, [selectedTabsStatus]);
  return (
    <>
      <div className="relative container mx-auto w-full px-3 mt-0 overflow-hidden h-[calc(100vh-130px)] max-md:h-[calc(100vh-17px)] ">
        <Tabs.Root
          defaultValue="Posts"
          value={isProfile}
          className="border-0 overflow-scroll h-full hide-scroll"
        >
          <div className="sticky top-0 z-50 pb-2 flex flex-row items-center justify-center w-full bg-white">
            <Tabs.List
              className="TabsListBg w-[400px] max-md:w-full"
              aria-label="Manage your account"
              style={
                {
                  "--active-bg-color": activeBgColor,
                } as React.CSSProperties
              }
            >
              <Tabs.Trigger
                className={"TabsTriggerBg"}
                value="Posts"
                style={{}}
                onClick={() => {
                  setActiveTabs("Posts"), setActivePostCategory(""), setIsProfile("Posts");
                  setSelectedTabs("Posts");
                }}
              >
                Posts
              </Tabs.Trigger>
              <Tabs.Trigger
                className="TabsTriggerBg"
                value="Services"
                onClick={() => {
                  setActiveTabs("Services"), setIsProfile("Services"), setSelectedTabs("Services");
                }}
              >
                Services
              </Tabs.Trigger>
              <Tabs.Trigger
                className="TabsTriggerBg"
                value="Events"
                onClick={() => {
                  setActiveTabs("Events"), setIsProfile("Events"), setSelectedTabs("Events");
                }}
              >
                Events
              </Tabs.Trigger>
              <Tabs.Trigger
                className="TabsTriggerBg"
                value="Profiles"
                onClick={() => {
                  setActiveTabs("Profiles"), setIsProfile("Profiles"), setSelectedTabs("Profiles");
                }}
              >
                Profiles
              </Tabs.Trigger>
            </Tabs.List>
          </div>

          <div className="pb-12">
            <TabsContent
              value="Posts"
              className="w-full"
              style={{ background: "white", width: "100%" }}
              ref={postsRef}
            >
              <div className="row justify-center  w-full bg-[#fafafa] sticky top-12 max-md:top-10 -mt-3 md:min-h-[70px]  z-[50]">
                <div className="row gap-5 max-md:gap-2 py-2 justify-between flex w-full px-4 max-md:px-1">
                  <div className="row gap-3 items-center pt-1">
                    {profile?.profileData?.categories !== null &&
                      profile?.profileData?.categories?.map((item: any, index: any) => (
                        <div key={index}>
                          {Object.entries(themes).map(([_, innerThemeProperties]) => (
                            <div key={innerThemeProperties.title}>
                              {(item === "Storytelling" ? "Literature" : item) ===
                                innerThemeProperties.title && (
                                <Button
                                  variant="outline"
                                  className="font-bold h-[30px] max-md:text-xs max-md:font-medium max-md:px-2"
                                  style={
                                    item == activePostCategory
                                      ? {
                                          color: innerThemeProperties.backgroundColor,
                                          borderColor: innerThemeProperties.backgroundColor,
                                        }
                                      : {
                                          borderColor: "gray",
                                          color: "gray",
                                        }
                                  }
                                  onClick={() => setActivePostCategory(item)}
                                >
                                  {item}
                                </Button>
                              )}
                            </div>
                          ))}
                        </div>
                      ))}
                  </div>
                  <div className="row gap-3 items-center ">
                    <div className="text-center ">
                      <p className="small-heading max-md:text-base">{postsLength}</p>
                      <p className=" max-md:text-sm -mt-2">Posts</p>
                    </div>

                    <div className="text-center">
                      <p className="small-heading max-md:text-base">{starredPostsLength}</p>
                      <p className=" max-md:text-sm -mt-2">Starred</p>
                    </div>
                  </div>
                </div>
              </div>
              <PostsMyProfile
                borderColor={activeBgColor}
                isOtherProfile={isOtherProfile}
                otherUserID={otherUserID}
                activePostCategory={activePostCategory}
                isRefatch={isRefatch}
                setIsOpen={setIsOpen}
              />
            </TabsContent>
            <TabsContent value="Services" ref={postsRef}>
              <div className=" row justify-center  w-full bg-[#fafafa] sticky top-12 max-md:top-10 -mt-3 md:min-h-[70px]  z-[50]">
                <div className="row gap-5 max-md:gap-2 py-2 justify-between flex w-full px-4 max-md:px-1">
                  <div className="">
                    <p className="small-heading">
                      {profile?.profileData?.languages && profile?.profileData?.languages.length > 0
                        ? profile.profileData.languages.length > 1
                          ? `${profile.profileData.languages[0]} +${profile.profileData.languages.length - 1}`
                          : profile.profileData.languages[0]
                        : "No Language"}
                    </p>
                    <p className="small-subheading">Language</p>
                  </div>
                  <div className="">
                    <p className="small-heading">{orderStats ? orderStats : 0}</p>
                    <p className="small-subheading">Orders</p>
                  </div>
                  <div className="">
                    <p className="small-heading">{customOrderStats ? customOrderStats : 0}</p>
                    <p className="small-subheading">Сustom</p>
                  </div>
                </div>
              </div>

              {isOtherProfile ? (
                <OtherServicesMProfile
                  activeColor={activeBgColor}
                  isOtherProfile={isOtherProfile}
                  otherUserID={otherUserID}
                />
              ) : (
                <ServicesMyProfile
                  activeColor={activeBgColor}
                  categories={profile?.profileData?.categories}
                  setIsOpen={setIsOpen}
                />
              )}
            </TabsContent>
            <TabsContent value="Events" ref={postsRef}>
              <EventsMyProfile
                activeBorderColor={activeBgColor}
                otherUserID={otherUserID}
                setIsOpen={setIsOpen}
              />
            </TabsContent>
            <TabsContent
              value="Profiles"
              ref={postsRef}
              style={{ background: "white", width: "100%" }}
            >
              <ProfileMyProfile
                activeBorderColor={activeBgColor}
                otherUserID={otherUserID}
                isOtherProfileStatus={isOtherProfileStatus}
                setSelectedProfileTabs={setSelectedProfileTabs}
                selectedProfileTabs={selectedProfileTabs}
              />
            </TabsContent>
          </div>
        </Tabs.Root>
      </div>
    </>
  );
}

export default TabsMyProfile;
