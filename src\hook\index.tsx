"use client";
import { User } from "@/services/UserInterface";
import { getUserById } from "@/services/usersServices";
import { useEffect, useState } from "react";

// For ESLint
/* global localStorage */

const useAuth = () => {
  //   const [user, setUser] = useState(null);
  const [isLogin, setIsLogin] = useState(false);
  const [isLoginLens, setIsLoginLens] = useState(false);

  const [userId, setUserId] = useState("");
  const [lensUserId, setLensUserId] = useState("");

  const [users, setUsers] = useState<User | null>(null); // State for storing users
  const [userData, setUserData] = useState<User | null>(null);
  const [lensData, setLensData] = useState<Record<string, unknown> | null>(null);

  useEffect(() => {
    const user = JSON.parse(localStorage.getItem("user") || "{}");
    const lensUser = localStorage.getItem("lens-user")
      ? JSON.parse(localStorage.getItem("lens-user") || "{}")
      : {};
    // console.log("Retrieved User:", lensUser);
    if (lensUser?.address) {
      setIsLoginLens(true);
      setLensUserId(lensUser?.address);
      setLensData(lensUser);
    }
    if (user.uid) {
      setIsLogin(true);
      setUserId(user.uid);
      setUserData(user);
    }

    const fetchUsers = async () => {
      // setLoading(true);
      console.log("call ");

      const response = await getUserById(user.uid);
      // setLoading(false);
      // console.log(user.uid);

      // console.log(response);

      if (response.success) {
        // Ensure all required fields exist in each user object
        const usersWithDefaults = response || [];
        // console.log(usersWithDefaults);

        if (usersWithDefaults) {
          user.profile_name = usersWithDefaults?.user?.profile_name;
          user.avatar = usersWithDefaults?.user?.avatar;
          user.app_version = usersWithDefaults?.user?.app_version;

          localStorage.setItem("user", JSON.stringify(user));
        }

        setUsers(usersWithDefaults?.user); // Update state with users having all properties
      } else {
        // console.log(response?.error);
      }
    };

    fetchUsers();
  }, []);

  return {
    isLogin,
    userId,
    userData,
    users,
    isLoginLens,
    lensUserId,
    lensData,
  };
};

export default useAuth;
