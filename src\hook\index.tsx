"use client";
import { User } from "@/services/UserInterface";
import { getUserById } from "@/services/usersServices";
import { useEffect, useState } from "react";

// For ESLint
/* global localStorage */

const useAuth = () => {
  const [isLogin, setIsLogin] = useState(false);
  const [isLoginLens, setIsLoginLens] = useState(false);
  const [userId, setUserId] = useState("");
  const [lensUserId, setLensUserId] = useState("");
  const [users, setUsers] = useState<User | null>(null);
  const [userData, setUserData] = useState<User | null>(null);
  const [lensData, setLensData] = useState<Record<string, unknown> | null>(null);

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Parse user data from localStorage
        const storedUser = localStorage.getItem("user");
        const user = storedUser ? JSON.parse(storedUser) : {};

        // Parse lens user data from localStorage
        const storedLensUser = localStorage.getItem("lens-user");
        const lensUser = storedLensUser ? JSON.parse(storedLensUser) : {};

        // Set lens authentication state
        if (lensUser?.address) {
          setIsLoginLens(true);
          setLensUserId(lensUser.address);
          setLensData(lensUser);
        }

        // Set regular authentication state
        if (user?.uid) {
          setIsLogin(true);
          setUserId(user.uid);
          setUserData(user);

          // Fetch fresh user data from API - only once
          console.log("Fetching user data for:", user.uid);

          const response = await getUserById(user.uid);

          if (response.success && response.user) {
            setUsers(response.user);

            // Update localStorage with fresh user data
            user.profile_name = response.user.profile_name;
            user.avatar = response.user.avatar;
            user.app_version = response.user.app_version;
            localStorage.setItem("user", JSON.stringify(user));
            setUserData(user);
          }
        }
      } catch (error) {
        console.error("Error initializing auth:", error);
      }
    };

    initializeAuth();
  }, []); // Empty dependency array - only run once on mount

  return {
    isLogin,
    userId,
    userData,
    users,
    isLoginLens,
    lensUserId,
    lensData,
  };
};

export default useAuth;
