"use client";
import { User } from "@/services/UserInterface";
import { getUserById } from "@/services/usersServices";
import { useEffect, useState, useCallback } from "react";

// For ESLint
/* global localStorage */

const useAuth = () => {
  const [isLogin, setIsLogin] = useState(false);
  const [isLoginLens, setIsLoginLens] = useState(false);
  const [userId, setUserId] = useState("");
  const [lensUserId, setLensUserId] = useState("");
  const [users, setUsers] = useState<User | null>(null);
  const [userData, setUserData] = useState<User | null>(null);
  const [lensData, setLensData] = useState<Record<string, unknown> | null>(null);

  // Memoize the fetchUsers function to prevent recreation on every render
  const fetchUsers = useCallback(async (uid: string) => {
    if (!uid) return;

    console.log("Fetching user data for:", uid);

    try {
      const response = await getUserById(uid);

      if (response.success && response.user) {
        setUsers(response.user);

        // Update localStorage with fresh user data
        const currentUser = JSON.parse(localStorage.getItem("user") || "{}");
        if (currentUser.uid === uid) {
          currentUser.profile_name = response.user.profile_name;
          currentUser.avatar = response.user.avatar;
          currentUser.app_version = response.user.app_version;
          localStorage.setItem("user", JSON.stringify(currentUser));
          setUserData(currentUser);
        }
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
    }
  }, []);

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = () => {
      try {
        // Parse user data from localStorage
        const storedUser = localStorage.getItem("user");
        const user = storedUser ? JSON.parse(storedUser) : {};

        // Parse lens user data from localStorage
        const storedLensUser = localStorage.getItem("lens-user");
        const lensUser = storedLensUser ? JSON.parse(storedLensUser) : {};

        // Set lens authentication state
        if (lensUser?.address) {
          setIsLoginLens(true);
          setLensUserId(lensUser.address);
          setLensData(lensUser);
        }

        // Set regular authentication state
        if (user?.uid) {
          setIsLogin(true);
          setUserId(user.uid);
          setUserData(user);

          // Fetch fresh user data from API
          fetchUsers(user.uid);
        }
      } catch (error) {
        console.error("Error initializing auth:", error);
      }
    };

    initializeAuth();
  }, [fetchUsers]);

  return {
    isLogin,
    userId,
    userData,
    users,
    isLoginLens,
    lensUserId,
    lensData,
  };
};

export default useAuth;
