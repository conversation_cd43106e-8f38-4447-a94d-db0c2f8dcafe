"use client";
import { getUsersByCategoryWithPost, getUsersByIds } from "@/services/usersServices";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import BrowseData from "./browseUser/browseData";
import BrowseLensData from "./browseLensUser/browseLensData";
import BrowseSkeleton from "./BrowseSkeleton";
import { useAccountsBulkQuery, useFollowingQuery } from "@/graphql/test/generated";
import ScrollButton from "@/components/bottomArrow";
import useAuth from "@/hook";
import { FollowingOrderBy, PageSize } from "@/graphql/test/generated";
import { getId } from "@/services/authBridgeService";
import { getLensProfilesById } from "@/services/lensService";
import { getPaginatedUsersByCategoryPosts, getPostsByPostIdUserId } from "@/services/postService";
import { useFilter } from "@/context/FilterContext";

const Browse = ({ categoryName, postId }: any) => {
  const user = useAuth();
  const { filters, getServiceFilters } = useFilter();

  // Consolidated state management
  const [profiles, setProfiles] = useState<Array<{ localName: string }>>([]);
  const [profilesmy, setProfilesMy] = useState<Array<{ localName: string }>>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [pageNumber, setPageNumber] = useState(1);
  const [hasMoreUsers, setHasMoreUsers] = useState(true);
  const [isLoadingMoreUsers, setIsLoadingMoreUsers] = useState(false);
  const [userId, setUserId] = useState("");
  const [currentFollowingCursor, setCurrentFollowingCursor] = useState<string | null>(null);
  const [allFollowing, setAllFollowing] = useState<Array<{ localName: any }>>([]);

  // Refs for optimization
  const postsRef = useRef<HTMLDivElement>(null);
  const hasFetchedUsers = useRef(false);
  const loadMoreTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Memoized URL parameters parsing
  const { paramsUID, paramPID } = useMemo(() => {
    if (!postId) return { paramsUID: "", paramPID: "" };
    const splitedIds = postId.split("%20");
    return {
      paramsUID: splitedIds[1] || "",
      paramPID: splitedIds[0] || "",
    };
  }, [postId]);

  // Memoized categories to fetch
  const categoriesToFetch = useMemo(() => {
    if (categoryName === "Storytelling" || categoryName === "Literature") {
      return ["Storytelling", "Literature"];
    }
    return [decodeURIComponent(categoryName)];
  }, [categoryName]);

  const {
    data: profileData,
    error,
    isLoading,
    refetch,
  } = useAccountsBulkQuery(
    {
      request: {
        usernames: categoryName == "My Feed" ? profilesmy : profiles,
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled: categoryName == "My Feed" ? profilesmy.length > 0 : profiles.length > 0,
    }
  );

  // Optimized lens profiles fetching with memoization
  const fetchLensProfilesByCategory = useCallback(async (category: string) => {
    if (!category) return;

    try {
      const resp = await getLensProfilesById(category);
      const lens_profiles: Array<{ localName: string }> =
        resp?.lens_ids?.map((curr: any) => ({
          localName: curr,
        })) || [];

      // Only update if profiles have actually changed
      setProfiles((prev) => {
        if (JSON.stringify(prev) === JSON.stringify(lens_profiles)) {
          return prev;
        }
        return lens_profiles;
      });
    } catch (error) {
      console.error("Error fetching Lens profiles:", error);
    }
  }, []);

  // Optimized user fetching with memoized sorting function
  const sortPostsByDate = useCallback((posts: any[]) => {
    return [...posts].sort((a, b) => {
      const dateA = a.added_at?.seconds || a.lastEditDate?.seconds || 0;
      const dateB = b.added_at?.seconds || b.lastEditDate?.seconds || 0;
      return dateB - dateA; // Descending order (newest first)
    });
  }, []);

  const fetchUsers = useCallback(async () => {
    if (hasFetchedUsers.current) return;
    hasFetchedUsers.current = true;

    setLoading(true);

    try {
      const serviceFilters = getServiceFilters();
      const dataWithPost = await getUsersByCategoryWithPost(categoriesToFetch, serviceFilters);

      // Process users with optimized sorting
      const usersWithSortedPosts = dataWithPost.map((user: any) => {
        if (user.posts?.length > 0) {
          return { ...user, posts: sortPostsByDate(user.posts) };
        }
        return user;
      });

      // Filter out users with no posts
      const usersWithPosts = usersWithSortedPosts.filter((user: any) => user.posts?.length > 0);

      setUsers(usersWithPosts);
      setHasMoreUsers(usersWithPosts.length >= 10);
      setPageNumber(2);
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  }, [categoriesToFetch, getServiceFilters, sortPostsByDate]);

  // Load next page of users
  const loadNextUsers = useCallback(async () => {
    if (isLoadingMoreUsers || !hasMoreUsers || categoryName === "My Feed") return;

    setIsLoadingMoreUsers(true);

    let categoriesToFetch;
    if (categoryName === "Storytelling" || categoryName === "Literature") {
      categoriesToFetch = ["Storytelling", "Literature"];
    } else {
      categoriesToFetch = [decodeURIComponent(categoryName)];
    }

    try {
      // Get complete filter object for service call
      const serviceFilters = getServiceFilters();
      const dataWithPost = await getUsersByCategoryWithPost(categoriesToFetch, serviceFilters);

      // Sort and process the new users
      const usersWithSortedPosts = dataWithPost.map((user: any) => {
        if (user.posts && user.posts.length > 0) {
          const sortedPosts = [...user.posts].sort((a, b) => {
            const dateA = a.added_at
              ? a.added_at.seconds
              : a.lastEditDate
                ? a.lastEditDate.seconds
                : 0;
            const dateB = b.added_at
              ? b.added_at.seconds
              : b.lastEditDate
                ? b.lastEditDate.seconds
                : 0;
            return dateB - dateA;
          });
          return { ...user, posts: sortedPosts };
        }
        return user;
      });

      // Filter out users with no posts
      const usersWithPosts = usersWithSortedPosts.filter(
        (user: any) => user.posts && user.posts.length > 0
      );

      // Filter out any duplicates
      const newUsers = usersWithPosts.filter(
        (newUser: any) => !users.some((existingUser) => existingUser.id === newUser.id)
      );

      if (newUsers.length > 0) {
        setUsers((prevUsers) => [...prevUsers, ...newUsers]);
        setPageNumber((prevPage) => prevPage + 1);
        setHasMoreUsers(newUsers.length >= 10); // If we got 10 or more items, assume there might be more
      } else {
        setHasMoreUsers(false);
      }
    } catch (error) {
      console.error("Error loading more users:", error);
    } finally {
      setIsLoadingMoreUsers(false);
    }
  }, [categoryName, isLoadingMoreUsers, hasMoreUsers, pageNumber, users, filters]);

  const fetchFeeds = async (list: any) => {
    // Get complete filter object for service call
    const serviceFilters = getServiceFilters();
    const dataWithPosts = await getUsersByIds(list, serviceFilters);

    // Sort posts by date within each user
    const usersWithSortedPosts = dataWithPosts?.users?.map((user: any) => {
      if (user?.posts && user?.posts?.length > 0) {
        // Sort posts by date (newest first)
        const sortedPosts = [...user.posts].sort((a, b) => {
          const dateA = a.added_at
            ? a.added_at.seconds
            : a.lastEditDate
              ? a.lastEditDate.seconds
              : 0;
          const dateB = b.added_at
            ? b.added_at.seconds
            : b.lastEditDate
              ? b.lastEditDate.seconds
              : 0;
          return dateB - dateA; // Descending order (newest first)
        });
        return { ...user, posts: sortedPosts };
      }
      return user;
    });

    // Filter out users with no posts before setting state
    const usersWithPosts = usersWithSortedPosts.filter(
      (user) => user?.posts && user?.posts?.length > 0
    );

    setUsers(usersWithPosts);
    setLoading(false);
  };

  // Debounced effects to prevent excessive API calls
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (user?.users?.bookmarks && decodeURIComponent(categoryName) === "My Feed") {
        fetchFeeds(user?.users?.bookmarks);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [user?.users?.bookmarks, categoryName, filters]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (categoryName) {
        fetchLensProfilesByCategory(categoryName.toLowerCase());
        if (!user?.users?.bookmarks && categoryName !== "My Feed") {
          fetchUsers();
        }
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [categoryName, fetchLensProfilesByCategory, user?.users?.bookmarks, filters]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (loadMoreTimeoutRef.current) {
        clearTimeout(loadMoreTimeoutRef.current);
      }
    };
  }, []);

  // Optimized Scroll Handling
  const handleScroll = useCallback(() => {
    if (!postsRef.current || loading) return;

    const { scrollTop, scrollHeight, clientHeight } = postsRef.current;

    // If we're close to the bottom of the scroll area
    if (scrollTop + clientHeight >= scrollHeight - 100) {
      // Prevent multiple rapid calls by using a timeout
      if (loadMoreTimeoutRef.current) {
        clearTimeout(loadMoreTimeoutRef.current);
      }

      loadMoreTimeoutRef.current = setTimeout(() => {
        // Load more users if we can
        if (hasMoreUsers && !isLoadingMoreUsers && categoryName !== "My Feed") {
          loadNextUsers();
        }

        // Load more followings for the "My Feed" category
        if (categoryName === "My Feed" && following?.following?.pageInfo?.next) {
          setCurrentFollowingCursor(following.following.pageInfo.next);
        }
      }, 300);
    }
  }, [loading, hasMoreUsers, isLoadingMoreUsers, loadNextUsers, categoryName]);

  // Add the scroll event listener
  useEffect(() => {
    const scrollContainer = postsRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener("scroll", handleScroll);
      return () => {
        scrollContainer.removeEventListener("scroll", handleScroll);
      };
    }
  }, [handleScroll]);

  // Optimized lens user ID fetching
  const getLensUserId = useCallback(async () => {
    if (!user.userId) return;

    try {
      const resp = await getId({ id: user.userId });
      if (resp?.lens_code && resp.lens_code !== userId) {
        setUserId(resp.lens_code);
      }
    } catch (error) {
      console.error("Error fetching lens user ID:", error);
    }
  }, [user.userId, userId]);

  useEffect(() => {
    getLensUserId();
  }, [getLensUserId]);

  const { data: following } = useFollowingQuery(
    {
      request: {
        account: userId,
        pageSize: PageSize.Ten,
        orderBy: FollowingOrderBy.Desc,
        cursor: currentFollowingCursor,
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled: !!userId && categoryName === "My Feed",
    }
  );

  interface FollowingItem {
    localName: any;
  }

  // Optimized following data processing with deduplication
  useEffect(() => {
    if (
      following?.following?.items &&
      following.following.items.length > 0 &&
      categoryName === "My Feed"
    ) {
      const newFollowing: FollowingItem[] = following.following.items.map((item) => ({
        localName: item.following.username?.localName,
      }));

      // Efficient deduplication using Set for better performance
      setAllFollowing((prev) => {
        const existingNames = new Set(prev.map((item) => item.localName));
        const uniqueNew = newFollowing.filter((item) => !existingNames.has(item.localName));
        return uniqueNew.length > 0 ? [...prev, ...uniqueNew] : prev;
      });

      setProfiles((prev) => {
        const existingNames = new Set(prev.map((item) => item.localName));
        const uniqueNew = newFollowing.filter((item) => !existingNames.has(item.localName));
        return uniqueNew.length > 0 ? [...prev, ...uniqueNew] : prev;
      });
    }
  }, [following, categoryName]);

  // Sync profilesmy with allFollowing
  useEffect(() => {
    if (allFollowing.length > 0) {
      setProfilesMy(allFollowing);
    }
  }, [allFollowing]);

  // Filter valid accounts from profile data
  const validProfileAccounts = useMemo(() => {
    if (!profileData?.accountsBulk) return [];
    return profileData.accountsBulk.filter((item) => item && item.address);
  }, [profileData?.accountsBulk]);

  // fetch first row firebase data

  type UserData = {
    id: string;
    user_id: string;
    profile_name: string;
    user_name: string;
    location: string;
    avatar: string;
    profile_pic: string;
    is_followed_by_me: boolean;
    posts: any[];
  };

  const [userDataFirstRow, setUserDataFirstRow] = useState<UserData | null>(null);
  const [userPostsFirstRow, setUserPostsFirstRow]: any = useState([]);
  const fetchFirtsRowPosts = async () => {
    try {
      // Get complete filter object for service call
      const serviceFilters = getServiceFilters();
      const resp = await getPostsByPostIdUserId({
        category: categoryName,
        loggedInUserId: user.userId ? user.userId : "",
        postId: paramPID,
        userId: paramsUID,
        filters: serviceFilters,
      });

      if (resp?.user_name && resp.posts.length > 0) {
        const formattedUserData: UserData = {
          id: resp.user_id,
          user_id: resp.user_id,
          profile_name: resp.user_name,
          user_name: resp.user_name,
          location: resp.user_location,
          avatar: resp.user_profile_pic,
          profile_pic: resp.user_profile_pic,
          is_followed_by_me: resp.is_followed_by_me,
          posts: resp.posts || [],
        };

        setUserDataFirstRow(formattedUserData);
        setUserPostsFirstRow(resp.posts || []);
      } else {
        // setHasMorePosts(false);
      }
    } catch (error) {
      console.error("💥 Error fetching posts:", error);
      // setHasMorePosts(false);
    } finally {
      // setIsLoadingMorePosts(false);
    }
  };

  // Optimized first row posts fetching with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (postId && (categoryName !== "My Feed" || (paramPID && paramsUID))) {
        fetchFirtsRowPosts();
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [categoryName, paramPID, paramsUID, postId, filters]);

  type UserWithPosts = {
    user_id: string;
    user_name: string;
    user_location: string;
    user_profile_pic: string;
    is_followed_by_me: boolean;
    posts: any[];
  };

  const pageSize = 3;

  const [posts, setPosts] = useState<UserWithPosts[]>([]);
  const [lastVisibleTimestamp, setLastVisibleTimestamp] = useState<any>(null);
  const [hasMorePosts, setHasMorePosts] = useState(true);
  const [isLoadingMorePosts, setIsLoadingMorePosts] = useState(false);

  // FIXED: Improved fetchPosts with better logging
  const fetchPosts = useCallback(
    async (isInitialLoad: boolean = false) => {
      // Prevent multiple simultaneous requests
      if (isLoadingMorePosts && !isInitialLoad) {
        console.log("❌ Already loading, skipping");
        return;
      }

      // Don't fetch if there are no more posts and it's not an initial load
      if (!hasMorePosts && !isInitialLoad) {
        console.log("❌ No more posts available, skipping");
        return;
      }

      setIsLoadingMorePosts(true);
      if (isInitialLoad) {
        setLoading(true);
      }

      try {
        const startAfter = isInitialLoad ? undefined : lastVisibleTimestamp;

        // Get complete filter object for service call
        const serviceFilters = getServiceFilters();
        const resp = await getPaginatedUsersByCategoryPosts({
          category: categoryName === "My Feed" ? "my-feed" : categoryName,
          loggedInUserId: user.userId ? user.userId : "",
          pageSize,
          startAfterUserId: startAfter,
          exclude_user_id: paramsUID || "",
          filters: serviceFilters,
        });

        if (resp?.results && Array.isArray(resp.results) && resp.results.length > 0) {
          if (isInitialLoad) {
            setPosts(resp.results);
          } else {
            setPosts((prevPosts: UserWithPosts[]) => {
              const newPosts = [...prevPosts, ...resp.results];

              return newPosts;
            });
          }

          // Update pagination cursor
          if (resp?.startAfterUserId !== undefined) {
            setLastVisibleTimestamp(resp.startAfterUserId);
          }

          // Check if we have more posts to load
          const hasMore = resp.results.length >= pageSize;
          setHasMorePosts(hasMore);
        } else {
          setHasMorePosts(false);
          if (isInitialLoad) {
            setPosts([]);
          }
        }
      } catch (error) {
        console.error("💥 Error fetching posts:", error);
        setHasMorePosts(false);
      } finally {
        setIsLoadingMorePosts(false);
        if (isInitialLoad) {
          setLoading(false);
        }
      }
    },
    [
      categoryName,
      user.userId,
      pageSize,
      lastVisibleTimestamp,
      hasMorePosts,
      isLoadingMorePosts,
      posts.length,
      filters,
    ]
  );

  // FIXED: Proper useEffect without causing infinite loops
  useEffect(() => {
    // Reset pagination state
    setLastVisibleTimestamp(null);
    setHasMorePosts(true);
    setPosts([]);

    // Only fetch if we have necessary data
    if (categoryName && (categoryName !== "My Feed" || user.userId)) {
      fetchPosts(true);
    }
  }, [categoryName, user.userId, filters]); // Removed fetchPosts from dependencies

  // Optimized intersection observer for infinite scroll

  const lastPostRef = useCallback(
    (node: HTMLDivElement | null) => {
      // console.log("🎯 Setting up intersection observer for node:", !!node);

      if (isLoadingMorePosts) {
        console.log("⏳ Loading in progress, skipping observer setup");
        return;
      }

      // Disconnect previous observer
      if (observerRef.current) {
        observerRef.current.disconnect();
      }

      // Create new observer
      observerRef.current = new IntersectionObserver(
        (entries) => {
          const entry = entries[0];

          if (entry.isIntersecting && hasMorePosts && !isLoadingMorePosts) {
            console.log("🎯 INTERSECTION: Loading more posts...");
            fetchPosts(false);
          }
        },
        {
          rootMargin: "50px", // Trigger 50px before the element comes into view
          threshold: 0.1,
        }
      );

      if (node) {
        observerRef.current.observe(node);
        console.log("👁️ Observer attached to last post");
      }
    },
    [isLoadingMorePosts, hasMorePosts, fetchPosts]
  );

  // FIXED: Cleanup observer on unmount
  useEffect(() => {
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  return (
    <div className="relative mx-auto w-full px-3 mt-3 overflow-hidden h-[calc(100vh-155px)] ">
      <div className="absolute bottom-0 left-0 right-0 h-[80px] bg-gradient-to-t from-white pointer-events-none max-md:hidden z-50" />
      <button
        onClick={handleScroll}
        className="absolute bottom-5 left-1/2 p-2 rounded-full max-md:hidden z-50"
      >
        <ScrollButton scrollRef={postsRef} />
      </button>
      <div
        className="overflow-scroll hide-scroll h-full pb-16"
        ref={postsRef}
        onScroll={handleScroll}
      >
        {/* Show skeleton loader when loading */}
        {loading && posts.length === 0 && !postId && categoryName !== "My Feed" && (
          <BrowseSkeleton count={5} />
        )}

        {/* Show actual content when not loading */}
        {!(loading && posts.length === 0 && !postId && categoryName !== "My Feed") && (
          <div>
            {/* Check if category filtering is applied and current category is not selected */}
            {(() => {
              const currentCategory = decodeURIComponent(categoryName);

              // Check if filters are applied and if current category is in selected categories
              if (filters.categories && filters.categories.length > 0) {
                if (!filters.categories.includes(currentCategory)) {
                  // Current category is not in selected filters, show no data
                  return (
                    <div className="w-full mt-8 p-6 bg-gray-50 rounded-lg border border-gray-200 text-center">
                      <div className="flex flex-col items-center">
                        <svg
                          className="w-12 h-12 text-gray-400 mb-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.467-.881-6.08-2.33"
                          />
                        </svg>
                        <p className="text-lg font-medium text-gray-600">
                          No content found in this category
                        </p>
                        <p className="text-gray-500 mt-1">Try selecting different filter options</p>
                      </div>
                    </div>
                  );
                }
              }

              return null; // Continue with normal rendering
            })()}
            {/* first row by click any post */}
            {userDataFirstRow &&
              userPostsFirstRow &&
              !paramsUID?.startsWith("0x") &&
              decodeURIComponent(categoryName) !== "My Feed" && (
                <>
                  <BrowseData
                    // userId={userData.user_id} // ADDED: Missing userId prop
                    userData={userDataFirstRow}
                    postData={userPostsFirstRow}
                    postId={paramPID}
                    categoryName={categoryName} // FIXED: Removed decodeURIComponent
                  />
                </>
              )}
            {paramsUID?.startsWith("0x") && (
              <>
                {validProfileAccounts
                  .filter((item) => item.address === paramsUID)
                  .map((item, index) => (
                    <BrowseLensData
                      key={index}
                      refetch={refetch}
                      userData={item}
                      postId={paramPID}
                      userId={paramsUID}
                      categoryName={decodeURIComponent(categoryName)}
                    />
                  ))}
              </>
            )}

            {posts.map((item: UserWithPosts, index: number) => {
              const isLast = index === posts.length - 1;

              // FIXED: Complete userData mapping with all required fields
              const userData = {
                id: item.user_id,
                user_id: item.user_id, // Added for consistency
                profile_name: item.user_name,
                user_name: item.user_name, // Added for consistency
                location: item.user_location,
                avatar: item.user_profile_pic, // Added avatar field
                profile_pic: item.user_profile_pic,
                is_followed_by_me: item.is_followed_by_me,
                posts: item.posts || [], // Added posts array
              };

              const userPosts = item.posts || [];

              return (
                <div
                  key={`${item.user_id}-${index}`}
                  ref={isLast ? lastPostRef : null}
                  className=" border-gray-200 pb-4"
                >
                  <BrowseData
                    // userId={userData.user_id} // ADDED: Missing userId prop
                    userData={userData}
                    postData={userPosts}
                    postId={paramPID}
                    categoryName={categoryName} // FIXED: Removed decodeURIComponent
                  />
                </div>
              );
            })}

            {/* Show skeleton loader when loading more posts */}
            {isLoadingMorePosts && !hasMorePosts && <BrowseSkeleton count={2} />}

            {!isLoadingMorePosts &&
              // posts.length > 0 &&
              validProfileAccounts
                .filter((item) => item.address !== paramsUID)
                .map((item, index) => (
                  <BrowseLensData
                    key={`${item.address}-${index}`}
                    refetch={refetch}
                    userData={item}
                    userId={item.address}
                    postId={paramPID}
                    categoryName={decodeURIComponent(categoryName)}
                  />
                ))}

            {/* End of Content Messages */}
            {!hasMorePosts && !isLoadingMorePosts && posts.length > 0 && (
              <div className="text-center py-4 text-gray-500 bg-gray-50 rounded-lg">
                🎉 No more users to show. You've seen them all!
              </div>
            )}

            {!loading && posts.length === 0 && !hasMorePosts && (
              <div className="text-center py-8 text-gray-500">
                No users found for this category.
              </div>
            )}

            {/* Show skeleton loader when loading more users */}
            {isLoadingMoreUsers && <BrowseSkeleton count={1} />}

            {/* Show skeleton loader when loading following data */}
            {categoryName === "My Feed" && following === null && posts.length === 0 && (
              <BrowseSkeleton count={3} />
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default Browse;
